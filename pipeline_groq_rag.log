2025-05-28 11:02:32,967 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 11:02:32,978 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 11:02:32,978 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 11:02:32,978 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 11:02:37,478 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:02:37,479 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:02:38,765 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 11:02:39,766 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:02:40,286 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:02:41,288 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:02:43,911 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 11:02:44,912 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:02:59,036 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 11:03:00,037 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 11:03:06,762 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 11:03:07,765 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 11:03:07,765 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:03:07,765 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'wikipedia', 'company_official']}
2025-05-28 11:03:07,765 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 11:03:07,766 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 11:03:08,493 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:08,766 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:08,768 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 11:03:10,262 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:10,263 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 11:03:11,222 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:11,226 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 11:03:12,220 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:12,223 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 11:03:13,216 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:13,220 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 11:03:14,281 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:14,282 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.10)
2025-05-28 11:03:16,493 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:16,495 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.10)
2025-05-28 11:03:17,400 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:17,403 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 11:03:17,905 - src.groq_client - INFO - LLM extraction completed
2025-05-28 11:03:17,906 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 11:03:17,907 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 11:03:17,907 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 11:03:17,907 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 11:03:17,907 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:17,907 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 11:03:18,409 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:18,410 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 11:03:18,911 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:18,912 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 11:03:19,413 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:19,414 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 11:03:19,916 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:19,916 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 11:03:20,418 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:20,418 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 11:03:20,920 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 11:03:20,921 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "description"'
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 11:03:20,923 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 11:03:20,923 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 11:03:51,543 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:04:26,538 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:04:48,779 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:04:56,188 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:04:57,189 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:04:58,763 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:04:59,764 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:05:01,004 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:05:02,007 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 11:05:32,539 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:05:44,522 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:05:56,136 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:05:57,137 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:06:00,942 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:06:01,944 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:06:03,267 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:06:04,271 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 11:06:17,007 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 11:06:21,746 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-28 11:06:22,747 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:06:23,189 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-28 11:06:23,190 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:06:24,191 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-28 11:06:24,193 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but RAG extraction failed
2025-05-28 11:06:24,193 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 11:06:24,194 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-28 11:06:29,403 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:06:41,859 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:06:41,862 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:06:41,862 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:06:42,864 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:07:08,833 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:07:08,836 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 11:07:08,837 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 11:07:09,838 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-28 11:07:13,398 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-28 11:07:14,400 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-28 11:07:19,971 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:07:34,624 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:07:34,629 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-28 11:07:34,630 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-28 11:07:35,631 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:07:41,842 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:07:41,843 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-28 11:07:41,844 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-28 11:07:42,845 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:07:52,588 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:07:52,590 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 11:07:52,590 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 11:07:53,592 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-28 11:08:18,328 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:08:24,126 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:08:24,128 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:08:24,128 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:08:25,130 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:08:36,902 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:08:36,904 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 11:08:36,904 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-28 11:08:37,906 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:08:38,671 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:08:39,673 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 11:08:39,683 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 250']
2025-05-28 11:08:39,689 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_110839.json
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - ⏱️  Total time: 366.7s
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 11:08:39,693 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_110839.json
2025-05-28 11:08:39,694 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_groq_rag_20250528_110839.json
2025-05-28 11:08:39,694 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_groq_rag_20250528_110839.json
